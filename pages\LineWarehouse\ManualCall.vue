<template>
	<view class="container">
		<!-- 查询区域 -->
		<view class="query-section">
			<view class="form-card compact">
				<view class="form-item compact">
					<view class="form-label">
						<text class="label-text">开始时间</text>
					</view>
					<view class="form-value">
						<picker mode="date" @change="startDateConfirm">
							<view class="picker-display compact">
								{{ query.startTime ? formatDate(new Date(query.startTime)) : '请选择' }}
								<text class="arrow-icon">▶</text>
							</view>
						</picker>
					</view>
				</view>
				
				<view class="form-item compact">
					<view class="form-label">
						<text class="label-text">结束时间</text>
					</view>
					<view class="form-value">
						<picker mode="date" @change="endDateConfirm">
							<view class="picker-display compact">
								{{ query.endTime ? formatDate(new Date(query.endTime)) : '请选择' }}
								<text class="arrow-icon">▶</text>
							</view>
						</picker>
					</view>
				</view>
				
				<view class="form-item compact">
					<view class="form-label">
						<text class="label-text">叫料状态</text>
					</view>
					<view class="form-value">
						<picker :range="statusColumns[0]" range-key="label" @change="statusConfirm">
							<view class="picker-display compact">
								{{ query.callStatusText || '请选择' }}
								<text class="arrow-icon">▶</text>
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 按钮区域 -->
		<view class="button-section">
			<button class="primary-button small" @click="openCallMaterialPopup('PUBLIC')">叫料</button>
			<button class="primary-button small" @click="openCallMaterialPopup('FormulatedProduct')">叫IBC空桶</button>
			<button class="primary-button small" @click="searchCallList">查询</button>
		</view>
		
		<!-- 列表区域 -->
		<view class="list-section">
			<view class="list-content">
				<view class="call-item" v-for="item in callList" :key="item.ID" @click="goToCallDetail(item)">
					<view class="item-header">
						<text class="order-no">{{ item.RequestSheetNo }}</text>
						<text class="status">{{ getStatusText(item.CallStatus) }}</text>
					</view>
					<view class="item-body">
						<view class="item-row compact">
							<text class="label">工单:</text>
							<text class="value">{{ item.ProductionOrder }}</text>
						</view>
						<view class="item-row compact">
							<text class="label">类型:</text>
							<text class="value">{{ item.RequestType }}</text>
						</view>
						<view class="item-row compact">
							<text class="label">时间:</text>
							<text class="value">{{ formatDateTime(item.RequestTime) }}</text>
						</view>
					</view>
				</view>
				
				<view class="no-data" v-if="callList.length === 0">
					<text>暂无叫料单记录</text>
				</view>
			</view>
			
			<view class="pagination" v-if="callList.length > 0">
				<view class="page-info">
					第 {{ pageInfo.pageIndex }} 页 / 共 {{ pageInfo.pageCount }} 页
				</view>
				<view class="page-buttons">
					<button 
						class="page-button small" 
						:disabled="pageInfo.pageIndex <= 1" 
						@click="pageChange(pageInfo.pageIndex - 1)"
					>
						上一页
					</button>
					<button 
						class="page-button small" 
						:disabled="pageInfo.pageIndex >= pageInfo.pageCount" 
						@click="pageChange(pageInfo.pageIndex + 1)"
					>
						下一页
					</button>
				</view>
			</view>
		</view>
		
		<!-- 叫料弹窗 -->
		<view class="popup-mask" v-if="showCallPopup" @click="showCallPopup = false">
			<view class="popup-container stylish" @click.stop>
				<view class="popup-header stylish">
					<text class="popup-title">{{ callPopupTitle }}</text>
					<view class="popup-actions">
						<text class="action-icon" @click="minimizePopup">−</text>
						<text class="action-icon close" @click="showCallPopup = false">×</text>
					</view>
				</view>
				
				<view class="form-card compact">
					<view class="form-item compact">
						<view class="form-label">
							<text class="label-text">物料类别</text>
						</view>
						<view class="form-value">
							<text class="category-name">{{ selectedCategoryName }}</text>
						</view>
					</view>
					
					<view class="form-item compact">
						<view class="form-label">
							<text class="label-text">叫料类别</text>
						</view>
						<view class="form-value">
							<text class="request-type">{{ requestTypeName }}</text>
						</view>
					</view>
					
					<view class="form-item compact">
						<view class="form-label">
							<text class="label-text">物料</text>
						</view>
						<view class="form-value">
							<!-- 使用自定义搜索选择器 -->
							<view class="material-selector" @click="openMaterialSelector">
								<view v-if="selectedMaterialName" class="material-selected">
									<text class="material-code">{{ selectedMaterialCode }}</text>
									<text class="material-name">{{ selectedMaterialName }}</text>
								</view>
								<view v-else class="material-placeholder">
									<text>请选择物料</text>
								</view>
								<text class="arrow-icon">▶</text>
							</view>
						</view>
					</view>
					
					<view class="form-item compact" v-if="selectedMaterialId && versionColumns[0] && versionColumns[0].length > 0">
						<view class="form-label">
							<text class="label-text">物料版本</text>
						</view>
						<view class="form-value">
							<picker :range="versionColumns[0]" range-key="Version" @change="versionConfirm">
								<view class="picker-display compact">
									{{ selectedVersion || '请选择版本' }}
									<text class="arrow-icon">▶</text>
								</view>
							</picker>
						</view>
					</view>
					
					<view class="form-item compact" v-if="selectedMaterialId">
						<view class="form-label">
							<text class="label-text">叫料数量</text>
						</view>
						<view class="form-value">
							<input 
								v-model="quantity" 
								type="number"
								min="1"
								class="quantity-input small"
							/>
						</view>
					</view>
				</view>
				
				<view class="button-container">
					<button 
						class="primary-button small"
						@click="submitCall"
						:disabled="!selectedMaterialId"
					>
						提交叫料
					</button>
				</view>
			</view>
		</view>
		
		<!-- 物料选择弹窗 -->
		<view class="popup-mask" v-if="showMaterialSelector" @click="showMaterialSelector = false">
			<view class="popup-container stylish selector-popup" @click.stop>
				<view class="popup-header stylish">
					<text class="popup-title">选择物料</text>
					<view class="popup-actions">
						<text class="action-icon" @click="minimizeMaterialPopup">−</text>
						<text class="action-icon close" @click="showMaterialSelector = false">×</text>
					</view>
				</view>
				
				<view class="search-section">
					<input 
						placeholder="搜索物料编码或名称" 
						v-model="materialSearchKeyword" 
						@input="handleMaterialSearch"
						class="search-input small"
					/>
				</view>
				
				<view class="material-list">
					<scroll-view style="height: 400rpx;" scroll-y>
						<view 
							v-for="item in filteredMaterialList" 
							:key="item.MaterialId" 
							class="material-item"
							:class="{ 'material-item-selected': selectedMaterialId === item.MaterialId }"
							@click="selectMaterial(item)"
						>
							<view class="material-info">
								<text class="material-code">{{ item.MaterialCode }}</text>
								<text class="material-name">{{ item.MaterialName }}</text>
							</view>
							<text v-if="selectedMaterialId === item.MaterialId" class="checkmark">✓</text>
						</view>
						
						<view v-if="filteredMaterialList.length === 0" class="no-data">
							<text>暂无匹配物料</text>
						</view>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { configUrl } from "@/config/index.js";
	
	export default {
		data() {
			return {
				// 线边仓信息
				selectedStorageId: '',
				selectedStorageCode: '',
				selectedStorageName: '',
				
				// 物料类别信息
				categoryColumns: [],
				selectedCategoryId: '',
				selectedCategoryCode: '',
				selectedCategoryName: '',
				categoryList: [],
				
				// 请求类型
				requestTypeName: '',
				requestTypeCode: '',
				
				// 物料信息
				showMaterialSelector: false,
				materialColumns: [],
				selectedMaterialId: '',
				selectedMaterialCode: '',
				selectedMaterialName: '',
				selectedMaterialUnit: '', // 添加物料单位变量
				materialList: [],
				materialSearchKeyword: '',
				filteredMaterialList: [],
				
				// 版本信息
				showVersionPicker: false,
				versionColumns: [],
				selectedVersion: '',
				
				// 叫料数量
				quantity: 1,
				
				// 查询参数
				query: {
					startTime: new Date().setHours(0, 0, 0, 0),
					endTime: new Date().setHours(0, 0, 0, 0),
					callStatus: '',
					callStatusText: '',
					callOrderNo: ''
				},
				showStartDatePicker: false,
				showEndDatePicker: false,
				showStatusPicker: false,
				statusColumns: [[
					{ label: '全部', value: '' },
					{ label: '未叫料', value: '0' },
					{ label: '已叫料', value: '1' },
					{ label: '已完成', value: '2' },
					{ label: '叫料中', value: '3' },
					{ label: '叫料失败', value: '4' }
				]],
				
				// 叫料单列表
				callList: [],
				pageInfo: {
					dataCount: 0,
					pageIndex: 1,
					pageSize: 5,
					pageCount: 0
				},
				
				// 叫料弹窗
				showCallPopup: false,
				callPopupTitle: '',
				
				// 当前叫料类型
				currentCallType: '' // PUBLIC 或 FormulatedProduct
			}
		},
		
		onLoad() {
			// 加载线边仓信息
			this.loadStorageInfo();
			
			// 如果线边仓信息为空，跳转到设置页面
			if (!this.selectedStorageId || !this.selectedStorageCode || !this.selectedStorageName) {
				uni.navigateTo({
					url: '/pages/main/mine'
				});
				return;
			}
			
			// 加载物料类别列表
			this.loadCategoryList();
			
			// 加载叫料单列表
			this.loadCallList();
		},
		
		methods: {
			// 加载线边仓信息
			loadStorageInfo() {
				try {
					this.selectedStorageId = uni.getStorageSync('StorageId') || '';
					this.selectedStorageCode = uni.getStorageSync('StorageCode') || '';
					this.selectedStorageName = uni.getStorageSync('StorageName') || '';
				} catch (e) {
					console.error('加载线边仓信息失败', e);
				}
			},
			
			// 加载物料类别列表
			loadCategoryList() {
				uni.request({
					url: `${configUrl.baseURL_DFM}/api/Category/GetList`,
					method: 'POST',
					header: {
						'Authorization': 'Bearer ' + uni.getStorageSync('token'),
						'Content-Type': 'application/json'
					},
					data: {},
					success: (res) => {
						if (res.statusCode === 200 && res.data.success) {
							this.categoryList = res.data.response || [];
							this.categoryColumns = [this.categoryList];
						} else {
							uni.showToast({
								title: '获取物料类别列表失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.showToast({
							title: '请求物料类别列表失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 开始时间确认
			startDateConfirm(e) {
				this.query.startTime = e.detail.value;
			},
			
			// 结束时间确认
			endDateConfirm(e) {
				this.query.endTime = e.detail.value;
			},
			
			// 状态确认
			statusConfirm(e) {
				const selectedIndex = e.detail.value;
				this.query.callStatus = this.statusColumns[0][selectedIndex].value;
				this.query.callStatusText = this.statusColumns[0][selectedIndex].label;
			},
			
			// 打开叫料弹窗
			openCallMaterialPopup(categoryCode) {
				this.currentCallType = categoryCode;
				
				if (categoryCode === 'PUBLIC') {
					this.callPopupTitle = '叫料（公共物料）';
					this.selectedCategoryName = '公共物料';
					this.requestTypeName = '公共物料';
					this.requestTypeCode = 'MATERIAL';
					this.selectedCategoryCode = 'PUBLIC';
				} else if (categoryCode === 'FormulatedProduct') {
					this.callPopupTitle = '叫IBC空桶';
					this.selectedCategoryName = 'IBC空桶';
					this.requestTypeName = 'IBC空桶';
					this.requestTypeCode = 'EMPTY_IBC';
					this.selectedCategoryCode = 'FormulatedProduct';
				}
				
				// 清空之前选择的物料
				this.selectedMaterialId = '';
				this.selectedMaterialCode = '';
				this.selectedMaterialName = '';
				this.selectedMaterialUnit = ''; // 清空物料单位
				this.selectedVersion = '';
				this.quantity = 1;
				
				// 加载物料列表
				this.loadMaterialList();
				
				// 显示弹窗
				this.showCallPopup = true;
			},
			
			// 加载物料列表
			loadMaterialList() {
				uni.request({
					url: `${configUrl.baseURL_DFM}/api/PromatWarehouseMapping/GetList`,
					method: 'POST',
					header: {
						'Authorization': 'Bearer ' + uni.getStorageSync('token'),
						'Content-Type': 'application/json'
					},
					data: {
						WarehouseCode: this.selectedStorageCode,
						MaterialType: this.selectedCategoryCode
					},
					success: (res) => {
						if (res.statusCode === 200 && res.data.success) {
							const rawData = res.data.response || [];
							// 根据物料ID、名称和编码进行去重
							const uniqueMap = new Map();
							rawData.forEach(item => {
								const key = `${item.MaterialId}-${item.MaterialCode}-${item.MaterialName}`;
								if (!uniqueMap.has(key)) {
									uniqueMap.set(key, item);
								}
							});
							this.materialList = Array.from(uniqueMap.values());
							
							// 添加显示文本
							this.materialList.forEach(item => {
								item.displayText = item.MaterialCode + ' ' + item.MaterialName;
							});
							this.filteredMaterialList = [...this.materialList];
							this.materialColumns = [this.filteredMaterialList];
						} else {
							uni.showToast({
								title: '获取物料列表失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.showToast({
							title: '请求物料列表失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 打开物料选择器
			openMaterialSelector() {
				this.showMaterialSelector = true;
				// 重置搜索关键词
				this.materialSearchKeyword = '';
				this.filteredMaterialList = [...this.materialList];
			},
			
			// 选择物料
			selectMaterial(material) {
				this.selectedMaterialId = material.MaterialId;
				this.selectedMaterialCode = material.MaterialCode;
				this.selectedMaterialName = material.MaterialName;
				this.selectedMaterialUnit = material.MaterialUnit || ''; // 保存物料单位
				this.showMaterialSelector = false;
				
				// 清空之前选择的版本
				this.selectedVersion = '';
				
				// 加载物料版本信息
				this.loadMaterialVersions();
			},
			
			// 加载物料版本
			loadMaterialVersions() {
				uni.request({
					url: `${configUrl.baseURL_DFM}/api/MaterialVersion/GetMaterialVersionList`,
					method: 'POST',
					header: {
						'Authorization': 'Bearer ' + uni.getStorageSync('token'),
						'Content-Type': 'application/json'
					},
					data: {
						MaterialId: this.selectedMaterialId
					},
					success: (res) => {
						if (res.statusCode === 200 && res.data.success) {
							const versions = res.data.response || [];
							this.versionColumns = [versions];
						} else {
							// 版本获取失败时清空版本列表
							this.versionColumns = [[]];
						}
					},
					fail: (err) => {
						// 请求失败时清空版本列表
						this.versionColumns = [[]];
					}
				});
			},
			
			// 版本选择确认
			versionConfirm(e) {
				const selectedIndex = e.detail.value;
				this.selectedVersion = this.versionColumns[0][selectedIndex].Version;
			},
			
			// 提交叫料
			submitCall() {
				if (!this.selectedMaterialId) {
					uni.showToast({
						title: '请选择物料',
						icon: 'none'
					});
					return;
				}
				
				if (this.quantity <= 0) {
					uni.showToast({
						title: '叫料数量必须大于0',
						icon: 'none'
					});
					return;
				}
				
				const params = {
					ProductionOrderId: '', // 工单ID需要从上下文获取或用户输入
					RequestType: this.requestTypeCode,
					CallTime: this.formatDate(new Date()),
					LineSideWarehouse: this.selectedStorageName,
					CallPoint: '', // 叫料点需要从上下文获取或用户输入
					CallMaterialType: 1, // 手动叫料
					Details: [{
						MaterialCode: this.selectedMaterialCode,
						MaterialName: this.selectedMaterialName,
						Quantity: parseInt(this.quantity),
						Unit: this.selectedMaterialUnit, // 使用选中物料的单位
						BatchNo: '',
						PalletNo: '',
						Remark: '',
						Version: this.selectedVersion || ''  // 添加版本信息，如果没有选择则为空
					}]
				};
				
				uni.request({
					url: `${configUrl.baseURL_Material}/api/CallMaterialSheet/AddCallMaterialSheet`,
					method: 'POST',
					header: {
						'Authorization': 'Bearer ' + uni.getStorageSync('token'),
						'Content-Type': 'application/json'
					},
					data: params,
					success: (res) => {
						if (res.statusCode === 200 && res.data.success) {
							uni.showToast({
								title: '叫料成功',
								icon: 'success'
							});
							
							// 关闭弹窗
							this.showCallPopup = false;
							
							// 重新加载叫料单列表
							this.loadCallList();
						} else {
							uni.showToast({
								title: res.data.message || '叫料失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.showToast({
							title: '叫料请求失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 格式化日期
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				
				return `${year}-${month}-${day}`;
			},
			
			// 格式化日期时间
			formatDateTime(datetime) {
				if (!datetime) return '';
				return datetime.replace('T', ' ').substring(0, 19);
			},
			
			// 跳转到叫料详情页面
			goToCallDetail(item) {
				// 将叫料单信息序列化后传递到详情页面
				const itemStr = encodeURIComponent(JSON.stringify(item));
				uni.navigateTo({
					url: `/pages/LineWarehouse/CallDetail?item=${itemStr}`
				});
			},
			
			// 获取状态文本
			getStatusText(status) {
				switch (status) {
					case '0': return '未叫料';
					case '1': return '已叫料';
					case '2': return '已完成';
					case '3': return '叫料中';
					case '4': return '叫料失败';
					default: return '未知';
				}
			},
			
			// 处理物料搜索
			handleMaterialSearch() {
				if (!this.materialSearchKeyword) {
					this.filteredMaterialList = [...this.materialList];
				} else {
					const lowerKeyword = this.materialSearchKeyword.toLowerCase();
					this.filteredMaterialList = this.materialList.filter(item => 
						item.MaterialCode.toLowerCase().includes(lowerKeyword) || 
						item.MaterialName.toLowerCase().includes(lowerKeyword)
					);
				}
			},
			
			// 加载叫料单列表
			loadCallList() {
				const params = {
					LineSideWarehouse: this.selectedStorageName,
					CallMaterialType: '1', // 手动叫料
					page: this.pageInfo.pageIndex,
					pageSize: this.pageInfo.pageSize,
					CallOrderNo: this.query.callOrderNo,
					StartTime: this.query.startTime ? this.formatDate(new Date(this.query.startTime)) : null,
					EndTime: this.query.endTime ? this.formatDate(new Date(this.query.endTime)) : null,
					CallMaterialStatus: this.query.callStatus
				};
				
				// 移除空值参数
				Object.keys(params).forEach(key => {
					if (params[key] === null || params[key] === undefined || params[key] === '') {
						delete params[key];
					}
				});
				
				uni.request({
					url: `${configUrl.baseURL_Material}/api/CallMaterialSheet/GetPageList`,
					method: 'POST',
					header: {
						'Authorization': 'Bearer ' + uni.getStorageSync('token'),
						'Content-Type': 'application/json'
					},
					data: params,
					success: (res) => {
						if (res.statusCode === 200 && res.data.success) {
							this.callList = res.data.response.data || [];
							this.pageInfo.dataCount = res.data.response.dataCount;
							this.pageInfo.pageIndex = res.data.response.page;
							this.pageInfo.pageSize = res.data.response.pageSize;
							this.pageInfo.pageCount = res.data.response.pageCount;
						} else {
							uni.showToast({
								title: '获取叫料单列表失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.showToast({
							title: '请求叫料单列表失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 查询叫料单
			searchCallList() {
				this.pageInfo.pageIndex = 1;
				this.loadCallList();
			},
			
			// 分页变化
			pageChange(page) {
				this.pageInfo.pageIndex = page;
				this.loadCallList();
			},
			
			// 最小化叫料弹窗
			minimizePopup() {
				uni.showToast({
					title: '最小化功能待实现',
					icon: 'none'
				});
			},
			
			// 最小化物料选择弹窗
			minimizeMaterialPopup() {
				uni.showToast({
					title: '最小化功能待实现',
					icon: 'none'
				});
			}
		}
	}
</script>

<style scoped>
	.container {
		padding: 15rpx;
		background-color: #f8f9fa;
		min-height: 100vh;
	}
	
	.query-section {
		margin-bottom: 20rpx;
	}
	
	.form-card {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
	}
	
	.form-card.compact {
		padding: 15rpx;
	}
	
	.form-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.form-item:last-child {
		border-bottom: none;
	}
	
	.form-item.compact {
		padding: 15rpx 0;
	}
	
	.form-label {
		flex: 1;
	}
	
	.label-text {
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
	}
	
	.form-value {
		flex: 2;
		text-align: right;
	}
	
	.storage-name {
		font-size: 24rpx;
		color: #666;
	}
	
	.request-type, .category-name {
		font-size: 24rpx;
		color: #2979ff;
		font-weight: 500;
	}
	
	.material-selector {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding: 15rpx 0;
	}
	
	.material-selected {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}
	
	.material-code {
		font-size: 22rpx;
		color: #333;
		font-weight: 500;
	}
	
	.material-name {
		font-size: 20rpx;
		color: #666;
		margin-top: 4rpx;
	}
	
	.material-placeholder {
		color: #999;
		font-size: 24rpx;
	}
	
	.picker-display {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding: 15rpx 0;
		color: #333;
	}
	
	.picker-display.compact {
		padding: 10rpx 0;
	}
	
	.arrow-icon {
		margin-left: 8rpx;
		color: #c0c0c0;
		font-size: 20rpx;
	}
	
	.input-field {
		padding: 8rpx;
		border: 1rpx solid #e0e0e0;
		border-radius: 6rpx;
		font-size: 24rpx;
		width: 100%;
		text-align: right;
	}
	
	.quantity-input {
		padding: 8rpx;
		border: 1rpx solid #e0e0e0;
		border-radius: 6rpx;
		font-size: 24rpx;
		width: 100rpx;
		text-align: center;
	}
	
	.quantity-input.small {
		padding: 6rpx;
		font-size: 22rpx;
		width: 80rpx;
	}
	
	.button-section {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
	}
	
	.primary-button {
		flex: 1;
		margin: 0 8rpx;
		background-color: #2979ff;
		color: white;
		border: none;
		padding: 16rpx;
		border-radius: 40rpx;
		font-size: 26rpx;
	}
	
	.primary-button.small {
		padding: 12rpx;
		font-size: 24rpx;
		border-radius: 30rpx;
	}
	
	.primary-button:disabled {
		background-color: #cccccc;
	}
	
	.list-section {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.list-content {
		max-height: 600rpx;
		overflow-y: auto;
	}
	
	.call-item {
		border: 1rpx solid #f0f0f0;
		border-radius: 8rpx;
		padding: 15rpx;
		margin-bottom: 15rpx;
	}
	
	.item-header {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
	}
	
	.order-no {
		font-size: 24rpx;
		font-weight: bold;
		color: #333;
	}
	
	.status {
		font-size: 20rpx;
		color: #2979ff;
	}
	
	.item-body {
		font-size: 22rpx;
	}
	
	.item-row {
		display: flex;
		margin-bottom: 8rpx;
	}
	
	.item-row.compact {
		margin-bottom: 5rpx;
	}
	
	.label {
		color: #999;
		width: 80rpx;
	}
	
	.value {
		color: #333;
		flex: 1;
	}
	
	.no-data {
		text-align: center;
		padding: 30rpx 0;
		color: #999;
		font-size: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.pagination {
		margin-top: 20rpx;
	}
	
	.page-info {
		text-align: center;
		margin-bottom: 15rpx;
		color: #666;
		font-size: 22rpx;
	}
	
	.page-buttons {
		display: flex;
		justify-content: center;
	}
	
	.page-button {
		background-color: #2979ff;
		color: white;
		border: none;
		padding: 12rpx 24rpx;
		margin: 0 6rpx;
		border-radius: 40rpx;
		font-size: 22rpx;
	}
	
	.page-button.small {
		padding: 10rpx 20rpx;
		font-size: 20rpx;
		border-radius: 30rpx;
	}
	
	.page-button:disabled {
		background-color: #cccccc;
	}
	
	.button-container {
		padding: 0 15rpx;
		margin-bottom: 20rpx;
	}
	
	.popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	}
	
	.popup-container {
		background-color: #fff;
		border-radius: 16rpx;
		width: 90%;
		max-width: 600rpx;
		max-height: 90vh;
		overflow-y: auto;
	}
	
	.popup-container.stylish {
		border-radius: 20rpx;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
	}
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		margin-bottom: 15rpx;
	}
	
	.popup-header.stylish {
		padding: 20rpx 25rpx;
		background-color: #f8f9fa;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
	}
	
	.popup-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}
	
	.popup-actions {
		display: flex;
	}
	
	.action-icon {
		width: 70rpx;
		height: 70rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
		font-weight: bold;
		color: #999;
		border-radius: 50%;
		margin-left: 15rpx;
	}
	
	.action-icon.close:hover {
		background-color: #ffebee;
		color: #f44336;
	}
	
	.action-icon:hover {
		background-color: #eeeeee;
		color: #666;
	}
	
	.search-section {
		padding: 15rpx 20rpx;
	}
	
	.search-input {
		padding: 12rpx;
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		font-size: 24rpx;
		width: 100%;
	}
	
	.search-input.small {
		padding: 10rpx;
		font-size: 22rpx;
	}
	
	.material-list {
		padding: 0 20rpx 15rpx;
	}
	
	.material-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 15rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.material-item:last-child {
		border-bottom: none;
	}
	
	.material-item-selected {
		background-color: #e3f2fd;
	}
	
	.checkmark {
		color: #2979ff;
		font-size: 32rpx;
		font-weight: bold;
	}
</style>