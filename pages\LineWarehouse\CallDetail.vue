<template>
	<view class="container">
		<!-- 页面标题 -->
		<view class="page-header">
			<text class="page-title">叫料详情</text>
		</view>
		
		<!-- 叫料主表信息 -->
		<view class="section">
			<view class="section-title">叫料单信息</view>
			<view class="form-card">
				<view class="form-item">
					<view class="form-label">叫料单号:</view>
					<view class="form-value">{{ callSheet.RequestSheetNo }}</view>
				</view>
				<view class="form-item">
					<view class="form-label">工单:</view>
					<view class="form-value">{{ callSheet.ProductionOrder }}</view>
				</view>
				<view class="form-item">
					<view class="form-label">状态:</view>
					<view class="form-value">{{ getStatusText(callSheet.CallStatus) }}</view>
				</view>
				<view class="form-item">
					<view class="form-label">叫料时间:</view>
					<view class="form-value">{{ formatDateTime(callSheet.RequestTime) }}</view>
				</view>
				<view class="form-item">
					<view class="form-label">叫料类型:</view>
					<view class="form-value">{{ callSheet.RequestType }}</view>
				</view>
				<view class="form-item">
					<view class="form-label">线边仓:</view>
					<view class="form-value">{{ callSheet.LineSideWarehouse }}</view>
				</view>
			</view>
		</view>
		
		<!-- 叫料明细信息 -->
		<view class="section">
			<view class="section-title">叫料明细</view>
			<view class="list-content">
				<view class="detail-item" v-for="(item, index) in callSheetDetails" :key="index">
					<view class="item-row">
						<text class="label">物料编码:</text>
						<text class="value">{{ item.MaterialCode }}</text>
					</view>
					<view class="item-row">
						<text class="label">物料名称:</text>
						<text class="value">{{ item.MaterialName }}</text>
					</view>
					<view class="item-row">
						<text class="label">规格型号:</text>
						<text class="value">{{ item.Specifications }}</text>
					</view>
					<view class="item-row">
						<text class="label">单位:</text>
						<text class="value">{{ item.Unit }}</text>
					</view>
					<view class="item-row">
						<text class="label">数量:</text>
						<text class="value">{{ item.Quantity }}</text>
					</view>
					<view class="item-row">
						<text class="label">版本:</text>
						<text class="value">{{ item.Version }}</text>
					</view>
				</view>
				
				<view class="no-data" v-if="callSheetDetails.length === 0">
					<text>暂无叫料明细</text>
				</view>
			</view>
		</view>
		
		<!-- 重新叫料按钮 -->
		<view class="button-section">
			<button class="primary-button" @click="reCallMaterial">重新叫料</button>
		</view>
	</view>
</template>

<script>
	import { configUrl } from "@/config/index.js";
	
	export default {
		data() {
			return {
				callSheet: {},
				callSheetDetails: []
			};
		},
		
		onLoad(options) {
			// 从参数中解析叫料单信息
			const itemStr = decodeURIComponent(options.item);
			const item = JSON.parse(itemStr);
			
			// 设置叫料单主表信息和明细信息
			this.callSheet = item;
			this.callSheetDetails = item.Details || [];
		},
		
		methods: {
			// 重新叫料
			reCallMaterial() {
				uni.showModal({
					title: '确认重新叫料',
					content: '确定要重新叫料吗？',
					success: (res) => {
						if (res.confirm) {
							uni.request({
								url: `${configUrl.baseURL_Material}/api/CallMaterialSheet/ReCallMaterialSheet?sheetId=${this.callSheet.ID}`,
								method: 'GET',
								header: {
									'Authorization': 'Bearer ' + uni.getStorageSync('token')
								},
								success: (res) => {
									if (res.statusCode === 200 && res.data.success) {
										uni.showToast({
											title: '重新叫料成功',
											icon: 'success'
										});
										
										// 重新加载页面数据
										this.refreshPage();
									} else {
										uni.showToast({
											title: res.data.msg || '重新叫料失败',
											icon: 'none'
										});
									}
								},
								fail: (err) => {
									uni.showToast({
										title: '重新叫料请求失败',
										icon: 'none'
									});
								}
							});
						}
					}
				});
			},
			
			// 刷新页面数据
			refreshPage() {
				// 重新加载页面数据
				const pages = getCurrentPages();
				const prevPage = pages[pages.length - 2]; // 上一个页面
				if (prevPage) {
					prevPage.$vm.loadCallList(); // 调用上一个页面的加载列表方法
				}
				
				// 返回上一页
				uni.navigateBack();
			},
			
			// 格式化日期时间
			formatDateTime(datetime) {
				if (!datetime) return '';
				return datetime.replace('T', ' ').substring(0, 19);
			},
			
			// 获取状态文本
			getStatusText(status) {
				switch (status) {
					case '0': return '未叫料';
					case '1': return '已叫料';
					case '2': return '已完成';
					case '3': return '叫料中';
					case '4': return '叫料失败';
					default: return '未知';
				}
			}
		}
	};
</script>

<style scoped>
	.container {
		padding: 20rpx;
	}
	
	.page-header {
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.page-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.section {
		background: #fff;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.section-title {
		padding: 20rpx;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		border-bottom: 1rpx solid #eee;
	}
	
	.form-card {
		padding: 20rpx;
	}
	
	.form-item {
		display: flex;
		margin-bottom: 15rpx;
	}
	
	.form-label {
		width: 180rpx;
		font-size: 28rpx;
		color: #666;
	}
	
	.form-value {
		flex: 1;
		font-size: 28rpx;
		color: #333;
	}
	
	.list-content {
		padding: 20rpx;
	}
	
	.detail-item {
		padding: 20rpx;
		border: 1rpx solid #eee;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}
	
	.item-row {
		display: flex;
		margin-bottom: 10rpx;
	}
	
	.item-row .label {
		width: 150rpx;
		font-size: 26rpx;
		color: #666;
	}
	
	.item-row .value {
		flex: 1;
		font-size: 26rpx;
		color: #333;
	}
	
	.no-data {
		text-align: center;
		padding: 40rpx;
		color: #999;
	}
	
	.button-section {
		padding: 20rpx;
		text-align: center;
	}
	
	.primary-button {
		width: 80%;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #007AFF;
		color: #fff;
		border-radius: 40rpx;
		font-size: 32rpx;
	}
</style>